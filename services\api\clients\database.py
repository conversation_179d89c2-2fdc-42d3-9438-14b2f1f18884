"""
Database client for Supabase operations.

This module provides functionality to interact with Supabase database
for storing and retrieving volunteer profile data using the Python client.
"""

import asyncio
from typing import Dict, Any, Optional, List
from uuid import UUID
from datetime import datetime
import json

from supabase import create_client, Client
from postgrest.exceptions import APIError

from common.settings import settings
from common.logging import get_logger

logger = get_logger(__name__)


class DatabaseClient:
    """
    Client for interacting with Supabase database.
    
    This client provides methods for storing, retrieving, and managing
    volunteer profile data using the Supabase Python client.
    """
    
    def __init__(self):
        """Initialize the database client."""
        self.client: Optional[Client] = None
        self._initialized = False
    
    async def initialize(self):
        """Initialize the Supabase client."""
        if self._initialized:
            return
        
        try:
            # Create Supabase client
            self.client = create_client(
                settings.supabase_url,
                settings.supabase_anon_key
            )
            
            self._initialized = True
            logger.info(
                "Supabase client initialized successfully",
                url=settings.supabase_url
            )
            
        except Exception as e:
            logger.error(f"Failed to initialize Supabase client: {e}")
            raise
    
    async def ensure_tables_exist(self):
        """Ensure required tables exist in Supabase."""
        if not self._initialized:
            await self.initialize()
        
        try:
            # Check if volunteers table exists by trying to query it
            try:
                response = self.client.table("volunteers").select("id").limit(1).execute()
                logger.info("Volunteers table exists and is accessible")
            except APIError as e:
                if "relation" in str(e).lower() and "does not exist" in str(e).lower():
                    logger.warning("Volunteers table does not exist - will be created via Supabase dashboard or migrations")
                else:
                    logger.error(f"Error checking volunteers table: {e}")
            
            # Check processing_logs table
            try:
                response = self.client.table("processing_logs").select("id").limit(1).execute()
                logger.info("Processing logs table exists and is accessible")
            except APIError as e:
                if "relation" in str(e).lower() and "does not exist" in str(e).lower():
                    logger.warning("Processing logs table does not exist - will be created via Supabase dashboard or migrations")
                else:
                    logger.error(f"Error checking processing_logs table: {e}")
            
            logger.info("Database table check completed")
            
        except Exception as e:
            logger.error(f"Failed to check database tables: {e}")
            raise
    
    async def store_volunteer_profile(
        self,
        volunteer_id: UUID,
        email: str,
        profile_data: Dict[str, Any],
        source_type: str = "linkedin"
    ) -> bool:
        """
        Store or update volunteer profile data.
        
        Args:
            volunteer_id: UUID of the volunteer
            email: Email address of the volunteer
            profile_data: Dictionary containing profile information
            source_type: Source of the data (linkedin, resume, etc.)
            
        Returns:
            True if storage was successful
        """
        if not self._initialized:
            await self.initialize()
        
        try:
            # Extract key fields from profile data
            full_name = profile_data.get("full_name", "")
            headline = profile_data.get("headline")
            location = profile_data.get("location")
            about_summary = profile_data.get("about_summary")
            current_company = profile_data.get("current_company")
            current_position = profile_data.get("current_position")
            
            # Prepare volunteer data
            volunteer_data = {
                "id": str(volunteer_id),
                "email": email,
                "full_name": full_name,
                "headline": headline,
                "location": location,
                "about_summary": about_summary,
                "current_company": current_company,
                "current_position": current_position,
                "profile_data": profile_data,
                "source_types": [source_type],
                "updated_at": datetime.now().isoformat()
            }
            
            # Upsert volunteer record
            response = self.client.table("volunteers").upsert(
                volunteer_data,
                on_conflict="id"
            ).execute()
            
            if response.data:
                logger.info(
                    f"Successfully stored volunteer profile {volunteer_id}",
                    email=email,
                    source_type=source_type
                )
                return True
            else:
                logger.error(f"Failed to store volunteer profile {volunteer_id}: No data returned")
                return False
                
        except Exception as e:
            logger.error(
                f"Failed to store volunteer profile {volunteer_id}: {e}",
                email=email,
                source_type=source_type
            )
            return False
    
    async def get_volunteer_by_id(self, volunteer_id: UUID) -> Optional[Dict[str, Any]]:
        """
        Get volunteer profile by ID.
        
        Args:
            volunteer_id: UUID of the volunteer
            
        Returns:
            Dictionary containing volunteer data, or None if not found
        """
        if not self._initialized:
            await self.initialize()
        
        try:
            response = self.client.table("volunteers").select("*").eq("id", str(volunteer_id)).execute()
            
            if response.data:
                return response.data[0]
            return None
                
        except Exception as e:
            logger.error(f"Failed to get volunteer by ID {volunteer_id}: {e}")
            return None
    
    async def get_volunteer_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """
        Get volunteer profile by email.
        
        Args:
            email: Email address of the volunteer
            
        Returns:
            Dictionary containing volunteer data, or None if not found
        """
        if not self._initialized:
            await self.initialize()
        
        try:
            response = self.client.table("volunteers").select("*").eq("email", email).execute()
            
            if response.data:
                return response.data[0]
            return None
                
        except Exception as e:
            logger.error(f"Failed to get volunteer by email {email}: {e}")
            return None
    
    async def search_volunteers(
        self,
        query: Optional[str] = None,
        skills: Optional[List[str]] = None,
        location: Optional[str] = None,
        company: Optional[str] = None,
        limit: int = 20,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        Search volunteers with various filters.
        
        Args:
            query: Text search query
            skills: List of skills to filter by
            location: Location filter
            company: Company filter
            limit: Maximum number of results
            offset: Number of results to skip
            
        Returns:
            List of volunteer dictionaries
        """
        if not self._initialized:
            await self.initialize()
        
        try:
            # Start with base query
            supabase_query = self.client.table("volunteers").select("*")
            
            # Apply filters
            if query:
                # Search in multiple text fields
                supabase_query = supabase_query.or_(
                    f"full_name.ilike.%{query}%,"
                    f"headline.ilike.%{query}%,"
                    f"about_summary.ilike.%{query}%"
                )
            
            if location:
                supabase_query = supabase_query.ilike("location", f"%{location}%")
            
            if company:
                supabase_query = supabase_query.ilike("current_company", f"%{company}%")
            
            # Apply pagination
            supabase_query = supabase_query.range(offset, offset + limit - 1)
            
            # Execute query
            response = supabase_query.execute()
            
            return response.data or []
                
        except Exception as e:
            logger.error(f"Failed to search volunteers: {e}")
            return []
    
    async def create_processing_log(
        self,
        volunteer_id: UUID,
        source_type: str,
        status: str = "processing",
        source_url: Optional[str] = None,
        error_message: Optional[str] = None,
        processing_time_ms: Optional[int] = None
    ) -> bool:
        """
        Create a processing log entry.
        
        Args:
            volunteer_id: UUID of the volunteer
            source_type: Type of processing (linkedin, resume, etc.)
            status: Processing status
            source_url: Source URL if applicable
            error_message: Error message if failed
            processing_time_ms: Processing time in milliseconds
            
        Returns:
            True if creation was successful
        """
        if not self._initialized:
            await self.initialize()
        
        try:
            log_data = {
                "volunteer_id": str(volunteer_id),
                "source_type": source_type,
                "status": status,
                "source_url": source_url,
                "error_message": error_message,
                "processing_time_ms": processing_time_ms,
                "created_at": datetime.now().isoformat()
            }
            
            if status in ["completed", "failed"]:
                log_data["completed_at"] = datetime.now().isoformat()
            
            response = self.client.table("processing_logs").insert(log_data).execute()
            
            if response.data:
                logger.info(
                    f"Created processing log for volunteer {volunteer_id}",
                    source_type=source_type,
                    status=status
                )
                return True
            else:
                logger.error(f"Failed to create processing log: No data returned")
                return False
                
        except Exception as e:
            logger.error(
                f"Failed to create processing log for volunteer {volunteer_id}: {e}",
                source_type=source_type,
                status=status
            )
            return False
    
    async def get_statistics(self) -> Dict[str, Any]:
        """
        Get database statistics.
        
        Returns:
            Dictionary containing statistics
        """
        if not self._initialized:
            await self.initialize()
        
        try:
            # Get volunteer count
            volunteers_response = self.client.table("volunteers").select("id", count="exact").execute()
            volunteer_count = volunteers_response.count or 0
            
            # Get processing logs count
            logs_response = self.client.table("processing_logs").select("id", count="exact").execute()
            logs_count = logs_response.count or 0
            
            # Get recent activity (last 24 hours)
            recent_cutoff = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0).isoformat()
            recent_response = self.client.table("volunteers").select("id", count="exact").gte("updated_at", recent_cutoff).execute()
            recent_updates = recent_response.count or 0
            
            return {
                "total_volunteers": volunteer_count,
                "total_processing_logs": logs_count,
                "recent_updates_24h": recent_updates,
                "database_type": "supabase",
                "timestamp": datetime.now().isoformat()
            }
                
        except Exception as e:
            logger.error(f"Failed to get Supabase database statistics: {e}")
            return {
                "error": str(e),
                "database_type": "supabase",
                "timestamp": datetime.now().isoformat()
            }
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check on the Supabase database.
        
        Returns:
            Dictionary containing health check results
        """
        try:
            if not self._initialized:
                await self.initialize()
            
            # Test basic connectivity by querying a system table
            response = self.client.table("information_schema.tables").select("table_name").limit(1).execute()
            
            # Check if we can access our tables
            volunteers_accessible = False
            logs_accessible = False
            
            try:
                self.client.table("volunteers").select("id").limit(1).execute()
                volunteers_accessible = True
            except:
                pass
            
            try:
                self.client.table("processing_logs").select("id").limit(1).execute()
                logs_accessible = True
            except:
                pass
            
            return {
                "status": "healthy",
                "connected": True,
                "database_type": "supabase",
                "volunteers_table_accessible": volunteers_accessible,
                "processing_logs_table_accessible": logs_accessible,
                "client_initialized": self._initialized
            }
                
        except Exception as e:
            logger.error(f"Supabase database health check failed: {e}")
            return {
                "status": "unhealthy",
                "connected": False,
                "database_type": "supabase",
                "error": str(e)
            }
    
    async def close(self):
        """Close the database connection."""
        if self.client:
            # Supabase client doesn't need explicit closing
            self._initialized = False
            logger.info("Supabase client connection closed")


# Global client instance
_database_client = None


async def get_database_client() -> DatabaseClient:
    """
    Get the global Supabase database client instance.
    
    Returns:
        DatabaseClient instance
    """
    global _database_client
    
    if _database_client is None:
        _database_client = DatabaseClient()
        await _database_client.initialize()
        await _database_client.ensure_tables_exist()
    
    return _database_client


async def database_health_check() -> Dict[str, Any]:
    """
    Perform a health check on the database connection.
    
    Returns:
        Dictionary containing health check results
    """
    try:
        client = await get_database_client()
        return await client.health_check()
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return {
            "status": "unhealthy",
            "connected": False,
            "database_type": "supabase",
            "error": str(e)
        } 