"""
Ingestion endpoints for the skill extractor API.

This module provides endpoints for submitting LinkedIn profiles and resume PDFs
for direct processing and extraction.
"""

import asyncio
from datetime import datetime
from typing import Optional
from uuid import UUID

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, status
from fastapi.responses import JSONResponse

from common.models import (
    LinkedInIngestRequest, 
    ResumeIngestRequest, 
    IngestResponse
)
from common.settings import settings
from common.logging import get_logger
from common.utils import volunteer_id_from_email, generate_uuid
from services.api.middleware import get_current_user, require_role
from services.api.clients.browser_use import get_browser_use_client
from services.api.clients.database import get_database_client
from services.api.clients.qdrant import get_qdrant_client

logger = get_logger(__name__)

router = APIRouter()


async def process_linkedin_profile(
    volunteer_id: UUID,
    linkedin_url: str,
    email: Optional[str] = None
) -> bool:
    """
    Background task to process LinkedIn profile extraction.
    
    Args:
        volunteer_id: UUID of the volunteer
        linkedin_url: LinkedIn profile URL
        email: Optional email address
        
    Returns:
        True if processing was successful
    """
    try:
        logger.info(
            "Starting LinkedIn profile processing",
            volunteer_id=str(volunteer_id),
            linkedin_url=linkedin_url
        )
        
        # Get clients
        browser_client = get_browser_use_client()
        db_client = await get_database_client()
        qdrant_client = get_qdrant_client()
        
        # Extract LinkedIn profile data
        profile_data = await browser_client.extract_linkedin_profile(linkedin_url)
        
        if not profile_data:
            logger.error(
                "LinkedIn profile extraction failed",
                volunteer_id=str(volunteer_id),
                linkedin_url=linkedin_url
            )
            return False
        
        # Store profile data in database
        success = await db_client.upsert_volunteer(
            volunteer_id=volunteer_id,
            email=email or profile_data.get("email", ""),
            profile_data=profile_data,
            source_type="linkedin"
        )
        
        if not success:
            logger.error(
                "Failed to store volunteer profile in database",
                volunteer_id=str(volunteer_id)
            )
            return False
        
        # TODO: Generate and store vector embeddings
        # This would be implemented in the vector worker
        # For now, we'll skip the vector generation
        
        logger.info(
            "LinkedIn profile processing completed successfully",
            volunteer_id=str(volunteer_id),
            linkedin_url=linkedin_url
        )
        
        return True
        
    except Exception as e:
        logger.error(
            "LinkedIn profile processing failed",
            volunteer_id=str(volunteer_id),
            linkedin_url=linkedin_url,
            error=str(e),
            exc_info=True
        )
        return False


@router.post("/linkedin", response_model=IngestResponse)
async def ingest_linkedin_profile(
    request: LinkedInIngestRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(require_role("service_extract"))
):
    """
    Submit a LinkedIn profile URL for skill extraction.
    
    This endpoint:
    1. Validates the LinkedIn URL format
    2. Generates a deterministic volunteer ID from email (if provided)
    3. Starts background processing of the LinkedIn profile
    4. Returns the volunteer ID and processing status
    
    Requires 'service_extract' role for authentication.
    """
    
    logger.info(
        "LinkedIn ingestion request received",
        url=str(request.url),
        email=request.email,
        priority=request.priority,
        user_id=current_user.get("sub")
    )
    
    try:
        # Generate volunteer ID
        if request.email:
            volunteer_id = volunteer_id_from_email(request.email)
            logger.debug("Generated volunteer ID from email", volunteer_id=str(volunteer_id))
        else:
            volunteer_id = generate_uuid()
            logger.debug("Generated random volunteer ID", volunteer_id=str(volunteer_id))
        
        # Start background processing
        background_tasks.add_task(
            process_linkedin_profile,
            volunteer_id=volunteer_id,
            linkedin_url=str(request.url),
            email=request.email
        )
        
        # Log successful submission
        logger.info(
            "LinkedIn profile queued for processing",
            volunteer_id=str(volunteer_id),
            url=str(request.url),
            priority=request.priority
        )
        
        return IngestResponse(
            volunteer_id=volunteer_id,
            status="processing",
            message="LinkedIn profile submitted for processing",
            estimated_completion=datetime.utcnow()
        )
        
    except Exception as e:
        logger.error(
            "LinkedIn ingestion failed",
            url=str(request.url),
            error=str(e),
            user_id=current_user.get("sub"),
            exc_info=True
        )
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Ingestion failed",
                "message": "Failed to submit LinkedIn profile for processing",
                "type": "ingestion_error"
            }
        )


@router.post("/resume", response_model=IngestResponse)
async def ingest_resume_pdf(
    request: ResumeIngestRequest,
    current_user: dict = Depends(require_role("service_extract"))
):
    """
    Submit a resume PDF URL for skill extraction.
    
    This endpoint is a placeholder for future resume processing functionality.
    Currently returns a not implemented error.
    
    Requires 'service_extract' role for authentication.
    """
    
    logger.info(
        "Resume ingestion request received",
        pdf_url=str(request.pdf_url),
        filename=request.filename,
        email=request.email,
        priority=request.priority,
        user_id=current_user.get("sub")
    )
    
    # Resume processing not implemented yet
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail={
            "error": "Not implemented",
            "message": "Resume processing is not yet implemented",
            "type": "feature_not_implemented"
        }
    )


@router.get("/status/{volunteer_id}")
async def get_ingestion_status(
    volunteer_id: UUID,
    current_user: dict = Depends(get_current_user)
):
    """
    Get the processing status of a volunteer profile.
    
    This endpoint checks the database to see if a volunteer profile
    has been processed and returns the current status.
    """
    
    logger.info(
        "Ingestion status requested",
        volunteer_id=str(volunteer_id),
        user_id=current_user.get("sub")
    )
    
    try:
        # Get database client
        db_client = await get_database_client()
        
        # Check if volunteer exists in database
        volunteer_data = await db_client.get_volunteer_by_id(volunteer_id)
        
        if volunteer_data:
            return {
                "volunteer_id": volunteer_id,
                "status": "completed",
                "message": "Profile processing completed",
                "last_updated": volunteer_data.get("updated_at"),
                "source_types": volunteer_data.get("source_types", [])
            }
        else:
            return {
                "volunteer_id": volunteer_id,
                "status": "not_found",
                "message": "No processing record found for this volunteer ID"
            }
            
    except Exception as e:
        logger.error(
            "Failed to get ingestion status",
            volunteer_id=str(volunteer_id),
            error=str(e),
            exc_info=True
        )
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Status check failed",
                "message": "Failed to retrieve processing status",
                "volunteer_id": str(volunteer_id)
            }
        )


@router.delete("/cancel/{volunteer_id}")
async def cancel_ingestion(
    volunteer_id: UUID,
    current_user: dict = Depends(require_role("admin"))
):
    """
    Cancel processing for a volunteer profile.
    
    Since processing is now direct (not queued), this endpoint
    can only delete existing records if needed.
    
    Requires 'admin' role for authentication.
    """
    
    logger.info(
        "Ingestion cancellation requested",
        volunteer_id=str(volunteer_id),
        user_id=current_user.get("sub")
    )
    
    try:
        # Get database client
        db_client = await get_database_client()
        
        # Check if volunteer exists
        volunteer_data = await db_client.get_volunteer_by_id(volunteer_id)
        
        if not volunteer_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "error": "Volunteer not found",
                    "message": f"No volunteer found with ID {volunteer_id}",
                    "volunteer_id": str(volunteer_id)
                }
            )
        
        # Since we don't have queued processing, we can only provide status
        return {
            "volunteer_id": volunteer_id,
            "status": "completed",
            "message": "Profile processing has already completed",
            "note": "Direct processing cannot be cancelled once started"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to cancel ingestion",
            volunteer_id=str(volunteer_id),
            error=str(e),
            exc_info=True
        )
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Cancellation failed",
                "message": "Failed to process cancellation request",
                "volunteer_id": str(volunteer_id)
            }
        )


@router.get("/stats")
async def get_processing_statistics(
    current_user: dict = Depends(require_role("admin"))
):
    """
    Get processing statistics and metrics.
    
    This endpoint provides information about the current state of
    volunteer profile processing.
    
    Requires 'admin' role for authentication.
    """
    
    logger.info(
        "Processing statistics requested",
        user_id=current_user.get("sub")
    )
    
    try:
        # Get database client
        db_client = await get_database_client()
        
        # Get database statistics
        db_stats = await db_client.get_statistics()
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "processing_mode": "direct",
            "database_stats": db_stats,
            "message": "Statistics retrieved successfully"
        }
        
    except Exception as e:
        logger.error(
            "Failed to get processing statistics",
            error=str(e),
            user_id=current_user.get("sub"),
            exc_info=True
        )
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Statistics unavailable",
                "message": "Failed to retrieve processing statistics",
                "type": "stats_error"
            }
        ) 