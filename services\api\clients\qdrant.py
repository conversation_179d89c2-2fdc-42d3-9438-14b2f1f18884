"""
Qdrant client for vector database operations.

This module provides functionality to interact with Qdrant vector database
for storing and searching volunteer profile embeddings.
"""

import asyncio
from typing import List, Dict, Any, Optional, Union
from uuid import UUID

from qdrant_client import QdrantClient as QdrantClientCore
from qdrant_client.http import models
from qdrant_client.http.models import Distance, VectorParams, PointStruct, Filter, FieldCondition, MatchValue

from common.settings import settings
from common.logging import get_logger

logger = get_logger(__name__)


class QdrantClient:
    """
    Client for interacting with Qdrant vector database.
    
    This client provides methods for storing, retrieving, and searching
    volunteer profile embeddings in Qdrant.
    """
    
    def __init__(self):
        """Initialize the Qdrant client."""
        self.client = None
        self.collection_name = settings.qdrant_collection_name
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize the Qdrant client connection."""
        try:
            # Use URL for cloud connections, fallback to host/port for local
            if settings.qdrant_url.startswith(('http://', 'https://')):
                self.client = QdrantClientCore(
                    url=settings.qdrant_url,
                    api_key=settings.qdrant_api_key.get_secret_value() if settings.qdrant_api_key else None,
                    timeout=30.0
                )
                logger.info(
                    "Qdrant client initialized successfully with URL",
                    url=settings.qdrant_url,
                    collection=self.collection_name
                )
            else:
                self.client = QdrantClientCore(
                    host=settings.qdrant_host,
                    port=settings.qdrant_port,
                    api_key=settings.qdrant_api_key.get_secret_value() if settings.qdrant_api_key else None,
                    https=settings.qdrant_use_https,
                    timeout=30.0
                )
                logger.info(
                    "Qdrant client initialized successfully with host/port",
                    host=settings.qdrant_host,
                    port=settings.qdrant_port,
                    collection=self.collection_name
                )
            
        except Exception as e:
            logger.error(f"Failed to initialize Qdrant client: {e}")
            raise
    
    async def ensure_collection_exists(self) -> bool:
        """
        Ensure the collection exists, create it if it doesn't.
        
        Returns:
            True if collection exists or was created successfully
        """
        try:
            # Check if collection exists
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]
            
            if self.collection_name in collection_names:
                logger.info(f"Collection '{self.collection_name}' already exists")
                return True
            
            # Create collection with vector configuration
            logger.info(f"Creating collection '{self.collection_name}'")
            self.client.create_collection(
                collection_name=self.collection_name,
                vectors_config=VectorParams(
                    size=settings.vector_dimension,  # Default 384 for all-MiniLM-L6-v2
                    distance=Distance.COSINE
                )
            )
            
            logger.info(f"Collection '{self.collection_name}' created successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to ensure collection exists: {e}")
            return False
    
    async def upsert_point(
        self,
        volunteer_id: str,
        vector: List[float],
        metadata: Dict[str, Any]
    ) -> bool:
        """
        Upsert a vector point for a volunteer.
        
        Args:
            volunteer_id: Unique identifier for the volunteer
            vector: The embedding vector
            metadata: Additional metadata to store with the vector
            
        Returns:
            True if upsert was successful
        """
        try:
            # Ensure collection exists
            if not await self.ensure_collection_exists():
                return False
            
            # Create point
            point = PointStruct(
                id=volunteer_id,
                vector=vector,
                payload=metadata
            )
            
            # Upsert point
            self.client.upsert(
                collection_name=self.collection_name,
                points=[point]
            )
            
            logger.info(
                f"Successfully upserted vector for volunteer {volunteer_id}",
                vector_size=len(vector),
                metadata_keys=list(metadata.keys())
            )
            
            return True
            
        except Exception as e:
            logger.error(
                f"Failed to upsert vector for volunteer {volunteer_id}: {e}",
                vector_size=len(vector) if vector else 0
            )
            return False
    
    async def get_point(self, volunteer_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve a vector point by volunteer ID.
        
        Args:
            volunteer_id: Unique identifier for the volunteer
            
        Returns:
            Dictionary containing vector and metadata, or None if not found
        """
        try:
            # Get point
            points = self.client.retrieve(
                collection_name=self.collection_name,
                ids=[volunteer_id],
                with_vectors=True,
                with_payload=True
            )
            
            if not points:
                logger.warning(f"No vector found for volunteer {volunteer_id}")
                return None
            
            point = points[0]
            
            return {
                "id": str(point.id),
                "vector": point.vector,
                "payload": point.payload or {}
            }
            
        except Exception as e:
            logger.error(f"Failed to retrieve vector for volunteer {volunteer_id}: {e}")
            return None
    
    async def search_similar(
        self,
        query_vector: List[float],
        top_k: int = 10,
        score_threshold: Optional[float] = None,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for similar vectors.
        
        Args:
            query_vector: The query vector to search with
            top_k: Number of results to return
            score_threshold: Minimum similarity score
            filters: Additional filters to apply
            
        Returns:
            List of similar vectors with scores and metadata
        """
        try:
            # Ensure collection exists
            if not await self.ensure_collection_exists():
                return []
            
            # Build filter if provided
            qdrant_filter = None
            if filters:
                conditions = []
                for key, value in filters.items():
                    conditions.append(
                        FieldCondition(
                            key=key,
                            match=MatchValue(value=value)
                        )
                    )
                if conditions:
                    qdrant_filter = Filter(must=conditions)
            
            # Perform search
            search_result = self.client.search(
                collection_name=self.collection_name,
                query_vector=query_vector,
                limit=top_k,
                score_threshold=score_threshold,
                query_filter=qdrant_filter,
                with_payload=True
            )
            
            # Convert results
            results = []
            for hit in search_result:
                results.append({
                    "id": str(hit.id),
                    "score": hit.score,
                    "payload": hit.payload or {}
                })
            
            logger.info(
                f"Vector search completed",
                results_count=len(results),
                top_k=top_k,
                score_threshold=score_threshold
            )
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to search similar vectors: {e}")
            return []
    
    async def delete_point(self, volunteer_id: str) -> bool:
        """
        Delete a vector point by volunteer ID.
        
        Args:
            volunteer_id: Unique identifier for the volunteer
            
        Returns:
            True if deletion was successful
        """
        try:
            self.client.delete(
                collection_name=self.collection_name,
                points_selector=models.PointIdsList(
                    points=[volunteer_id]
                )
            )
            
            logger.info(f"Successfully deleted vector for volunteer {volunteer_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete vector for volunteer {volunteer_id}: {e}")
            return False
    
    async def get_collection_info(self) -> Dict[str, Any]:
        """
        Get information about the collection.
        
        Returns:
            Dictionary containing collection statistics
        """
        try:
            collection_info = self.client.get_collection(self.collection_name)
            
            # Build response with available attributes
            response = {
                "name": self.collection_name,
                "vectors_count": getattr(collection_info, 'vectors_count', 0),
                "points_count": getattr(collection_info, 'points_count', 0),
                "segments_count": getattr(collection_info, 'segments_count', 0),
            }

            # Add optional attributes if they exist
            if hasattr(collection_info, 'disk_data_size'):
                response["disk_data_size"] = collection_info.disk_data_size
            if hasattr(collection_info, 'ram_data_size'):
                response["ram_data_size"] = collection_info.ram_data_size

            # Add config if available
            try:
                response["config"] = {
                    "vector_size": collection_info.config.params.vectors.size,
                    "distance": collection_info.config.params.vectors.distance.value
                }
            except AttributeError:
                response["config"] = {"status": "config_unavailable"}

            return response
            
        except Exception as e:
            logger.error(f"Failed to get collection info: {e}")
            return {
                "name": self.collection_name,
                "error": str(e),
                "status": "unavailable"
            }
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check on the Qdrant client.
        
        Returns:
            Dictionary containing health check results
        """
        try:
            # Test basic connectivity
            collections = self.client.get_collections()
            
            # Check if our collection exists
            collection_names = [col.name for col in collections.collections]
            collection_exists = self.collection_name in collection_names
            
            # Get collection info if it exists
            collection_info = None
            if collection_exists:
                collection_info = await self.get_collection_info()
            
            return {
                "status": "healthy",
                "connected": True,
                "collection_exists": collection_exists,
                "collection_info": collection_info,
                "total_collections": len(collections.collections)
            }
            
        except Exception as e:
            logger.error(f"Qdrant health check failed: {e}")
            return {
                "status": "unhealthy",
                "connected": False,
                "error": str(e)
            }


# Global client instance
_qdrant_client = None


def get_qdrant_client() -> QdrantClient:
    """
    Get the global Qdrant client instance.
    
    Returns:
        QdrantClient instance
    """
    global _qdrant_client
    
    if _qdrant_client is None:
        _qdrant_client = QdrantClient()
    
    return _qdrant_client


async def qdrant_health_check() -> Dict[str, Any]:
    """
    Convenience function to perform a Qdrant health check.
    
    Returns:
        Dictionary containing health check results
    """
    client = get_qdrant_client()
    return await client.health_check() 