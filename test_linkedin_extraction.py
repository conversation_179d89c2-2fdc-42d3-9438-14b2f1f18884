#!/usr/bin/env python3
"""
Standalone LinkedIn Profile Extractor Test Script
"""

import os
import asyncio
import json
from pathlib import Path
from typing import List, Optional
from pydantic import BaseModel, Field, SecretStr
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import required libraries - make sure these are installed:
# pip install browser-use langchain-openai pydantic python-dotenv

try:
    from langchain_openai import AzureChatOpenAI
    from browser_use import Agent, Browser, BrowserConfig, Controller
except ImportError as e:
    print(f"Missing required package: {e}")
    print("Please install with: pip install browser-use langchain-openai pydantic python-dotenv")
    exit(1)

# Pydantic models for LinkedIn profile extraction
class LinkedInExperience(BaseModel):
    company_name: str
    job_title: str
    dates: str
    description: Optional[str] = None
    location: Optional[str] = None

class LinkedInEducation(BaseModel):
    institution_name: str
    degree: Optional[str] = None
    field_of_study: Optional[str] = None
    dates: Optional[str] = None
    grade: Optional[str] = None

class LinkedInCertification(BaseModel):
    name: str
    issuing_organization: str
    issue_date: Optional[str] = None
    expiration_date: Optional[str] = None
    credential_id: Optional[str] = None

class LinkedInVolunteerExperience(BaseModel):
    organization: str
    role: str
    cause: Optional[str] = None
    dates: Optional[str] = None
    description: Optional[str] = None

class LinkedInProject(BaseModel):
    name: str
    description: Optional[str] = None
    dates: Optional[str] = None
    associated_with: Optional[str] = None
    url: Optional[str] = None

class LinkedInProfileData(BaseModel):
    full_name: str
    headline: Optional[str] = None
    location: Optional[str] = None
    about_summary: Optional[str] = None
    work_experience: List[LinkedInExperience] = Field(default_factory=list)
    education: List[LinkedInEducation] = Field(default_factory=list)
    skills: List[str] = Field(default_factory=list)
    certifications: List[LinkedInCertification] = Field(default_factory=list)
    volunteer_experience: List[LinkedInVolunteerExperience] = Field(default_factory=list)
    projects: List[LinkedInProject] = Field(default_factory=list)
    languages: List[str] = Field(default_factory=list)
    recommendations_received: int = 0
    current_company: Optional[str] = None
    current_position: Optional[str] = None

def check_environment_variables():
    """Check if all required environment variables are set."""
    required_vars = [
        'LINKEDIN_EMAIL',
        'LINKEDIN_PASSWORD', 
        'AZURE_OPENAI_ENDPOINT',
        'AZURE_OPENAI_API_KEY',
        'AZURE_OPENAI_DEPLOYMENT_NAME'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("❌ Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\nPlease set these in your .env file or environment.")
        return False
    
    print("✅ All required environment variables are set.")
    return True

async def extract_linkedin_profile(profile_url: str) -> Optional[dict]:
    """
    Extract LinkedIn profile data using browser automation.
    """
    print(f"🔍 Starting extraction for: {profile_url}")
    
    # Get credentials from environment
    linkedin_email = os.getenv('LINKEDIN_EMAIL')
    linkedin_password = os.getenv('LINKEDIN_PASSWORD')
    azure_endpoint = os.getenv('AZURE_OPENAI_ENDPOINT')
    azure_key = os.getenv('AZURE_OPENAI_API_KEY')
    deployment_name = os.getenv('AZURE_OPENAI_DEPLOYMENT_NAME')
    api_version = os.getenv('AZURE_OPENAI_API_VERSION', '2025-01-01-preview')
    
    try:
        # Initialize the LLM
        print("🤖 Initializing Azure OpenAI...")
        llm = AzureChatOpenAI(
            model=deployment_name,
            api_version=api_version,
            azure_endpoint=azure_endpoint,
            api_key=SecretStr(azure_key),
            temperature=0.0,
        )

        # Initialize browser with more permissive settings
        print("🌐 Starting browser...")
        browser = Browser(
            config=BrowserConfig(
                headless=False,  # Keep visible for debugging
                disable_security=True,
                chrome_instance_path=None,  # Let it find Chrome automatically
            )
        )

        # Create controller
        controller = Controller(output_model=LinkedInProfileData)

        # Define the extraction task with more detailed instructions
        task = f"""
        You are a LinkedIn profile data extractor. Follow these steps:

        1. Navigate to https://www.linkedin.com/login
        2. Enter the username/email: {linkedin_email}
        3. Enter the password: {linkedin_password}
        4. Click the Sign In button
        5. Wait for login to complete (look for LinkedIn homepage or profile)
        6. Navigate to: {profile_url}
        7. Wait for the profile page to fully load
        8. Scroll down slowly to load all sections
        9. Look for and click "Show more" buttons to expand content
        10. Extract the following information:
            - Full name (from the profile header)
            - Headline/title
            - Location
            - About section summary
            - All work experience entries with company, title, dates, description. Scroll down and expand the experience section by clicking on Show all experiences.
            - Education entries
            - Skills (if visible)
            - Certifications (if present). Expand to capture all certificates. 
            - Volunteer experience (if present). Expand to capture all Volunteer Experiences. 
            - Projects (if present)
            - Languages (if present)

        Return the data in valid JSON format matching the LinkedInProfileData schema.
        If any section is not available, use empty lists or null values.
        Be thorough but don't get stuck - if something takes too long, move on.
        """

        # Sensitive data for login
        sensitive_data = {
            'username': linkedin_email,
            'password': linkedin_password
        }

        # Create and run the agent
        print("🤖 Running extraction agent...")
        agent = Agent(
            task=task,
            llm=llm,
            browser=browser,
            controller=controller,
            sensitive_data=sensitive_data
        )

        history = await agent.run()
        result = history.final_result()

        # Close browser
        await browser.close()
        print("🌐 Browser closed.")

        if not result:
            print("❌ Agent returned empty result.")
            return None

        # Parse the result
        try:
            if isinstance(result, str):
                linkedin_data = LinkedInProfileData.model_validate_json(result)
            else:
                linkedin_data = LinkedInProfileData.model_validate(result)
            
            print(f"✅ Successfully extracted profile for: {linkedin_data.full_name}")
            return linkedin_data.model_dump()
            
        except Exception as e:
            print(f"❌ Failed to parse result: {e}")
            print(f"Raw result: {result}")
            return None

    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        return None

def save_profile_data(profile_data: dict, profile_url: str):
    """Save the extracted profile data to a JSON file."""
    try:
        # Create output directory
        output_dir = Path("linkedin_extractions")
        output_dir.mkdir(exist_ok=True)
        
        # Generate filename from URL
        filename = profile_url.replace("https://www.linkedin.com/in/", "").replace("/", "_")
        if not filename:
            filename = "unknown_profile"
        filename = f"{filename}.json"
        
        filepath = output_dir / filename
        
        # Save to file
        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(profile_data, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Profile saved to: {filepath}")
        
    except Exception as e:
        print(f"❌ Error saving profile: {e}")

async def main():
    """Main function to run the LinkedIn extraction test."""
    print("🚀 LinkedIn Profile Extractor Test")
    print("=" * 40)
    
    # Check environment variables
    if not check_environment_variables():
        return
    
    # Get LinkedIn URL from user
    profile_url = input("\n📎 Enter LinkedIn profile URL: ").strip()
    
    if not profile_url:
        print("❌ No URL provided.")
        return
    
    if not profile_url.startswith("https://www.linkedin.com/in/"):
        print("❌ Please provide a valid LinkedIn profile URL (https://www.linkedin.com/in/...)")
        return
    
    print(f"\n🎯 Target URL: {profile_url}")
    
    # Run extraction
    try:
        profile_data = await extract_linkedin_profile(profile_url)
        
        if profile_data:
            print("\n📊 Extraction Results:")
            print("-" * 20)
            print(f"Name: {profile_data.get('full_name', 'N/A')}")
            print(f"Headline: {profile_data.get('headline', 'N/A')}")
            print(f"Location: {profile_data.get('location', 'N/A')}")
            print(f"Work Experience: {len(profile_data.get('work_experience', []))} entries")
            print(f"Education: {len(profile_data.get('education', []))} entries")
            print(f"Skills: {len(profile_data.get('skills', []))} items")
            
            # Save to file
            save_profile_data(profile_data, profile_url)
            
            # Show first work experience as example
            if profile_data.get('work_experience'):
                print(f"\n📋 First Work Experience:")
                exp = profile_data['work_experience'][0]
                print(f"   Company: {exp.get('company_name', 'N/A')}")
                print(f"   Title: {exp.get('job_title', 'N/A')}")
                print(f"   Dates: {exp.get('dates', 'N/A')}")
            
        else:
            print("❌ Extraction failed - no data returned.")
            
    except KeyboardInterrupt:
        print("\n⚠️ Extraction cancelled by user.")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
